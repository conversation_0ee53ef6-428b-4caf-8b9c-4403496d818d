import {
    SidebarMenu,
    SidebarMenuItem,
    SidebarMenuButton
} from '@/components/ui/sidebar';
import { NavItem } from '@/types';
import { Link } from '@inertiajs/react';
import { ChevronDown } from 'lucide-react';
import { useState } from 'react';

interface NavMainProps {
    items: NavItem[];
}

export function NavMain({ items }: NavMainProps) {
    const [openDropdown, setOpenDropdown] = useState<string | null>(null);

    return (
        <SidebarMenu>
            {items.map((item) => (
                <SidebarMenuItem key={item.title}>
                    {item.children ? (
                        <>
                            <SidebarMenuButton
                                onClick={() =>
                                    setOpenDropdown(openDropdown === item.title ? null : item.title)
                                }
                            >
                                {item.icon && <item.icon className="w-4 h-4 mr-2" />}
                                {item.title}
                                <ChevronDown className="ml-auto w-4 h-4" />
                            </SidebarMenuButton>

                            {openDropdown === item.title && (
                                <div className="ml-6 mt-1 space-y-1">
                                    {item.children.map((child) => (
                                        <Link
                                            key={child.title}
                                            href={child.href}
                                            className="block text-sm text-muted-foreground hover:text-primary"
                                        >
                                            {child.title}
                                        </Link>
                                    ))}
                                </div>
                            )}
                        </>
                    ) : (
                        <SidebarMenuButton asChild>
                            <Link href={item.href}>
                                {item.icon && <item.icon className="w-4 h-4 mr-2" />}
                                {item.title}
                            </Link>
                        </SidebarMenuButton>
                    )}
                </SidebarMenuItem>
            ))}
        </SidebarMenu>
    );
}
