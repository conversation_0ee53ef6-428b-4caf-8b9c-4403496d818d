import { useState } from 'react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import DataTable, { TableColumn } from 'react-data-table-component';

const breadcrumbs: BreadcrumbItem[] = [{ title: 'Data Guru', href: '/data-guru' }];

type Guru = {
    id: number;
    nama: string;
    nip: string;
    mapel: string;
};

const customStyles = {
    headCells: {
        style: {
            fontWeight: 'bold',
            fontSize: '14px',
            backgroundColor: '#f9fafb',
        },
    },
    rows: {
        style: {
            fontSize: '14px',
            minHeight: '48px',
        },
    },
};

export default function DataGuru() {
    const [data, setData] = useState<Guru[]>([
        { id: 1, nama: 'Budi <PERSON>o', nip: '12345678', mapel: 'Matema<PERSON><PERSON>' },
        { id: 2, nama: '<PERSON><PERSON>', nip: '87654321', mapel: 'Bahasa Indonesia' },
        { id: 3, nama: 'De<PERSON>', nip: '11223344', mapel: 'IPA' },
    ]);

    const [search, setSearch] = useState('');
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [form, setForm] = useState({ nama: '', nip: '', mapel: '' });

    const columns: TableColumn<Guru>[] = [
        {
            name: 'No',
            cell: (_row, index) => index + 1,
            width: '60px',
        },
        {
            name: 'Nama',
            selector: (row) => row.nama,
            sortable: true,
        },
        {
            name: 'NIP',
            selector: (row) => row.nip,
            sortable: true,
        },
        {
            name: 'Mata Pelajaran',
            selector: (row) => row.mapel,
            sortable: true,
        },
        {
            name: 'Aksi',
            cell: (row) => (
                <div className="flex gap-2">
                    <button className="text-sm text-blue-600 hover:underline">Edit</button>
                    <button className="text-sm text-red-600 hover:underline">Hapus</button>
                </div>
            ),
        },
    ];

    const filteredData = data.filter((guru) =>
        guru.nama.toLowerCase().includes(search.toLowerCase()) ||
        guru.nip.includes(search) ||
        guru.mapel.toLowerCase().includes(search.toLowerCase())
    );

    const handleAddGuru = () => {
        if (!form.nama || !form.nip || !form.mapel) return;
        const newGuru: Guru = {
            id: data.length + 1,
            nama: form.nama,
            nip: form.nip,
            mapel: form.mapel,
        };
        setData([...data, newGuru]);
        setForm({ nama: '', nip: '', mapel: '' });
        setIsModalOpen(false);
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Data Guru" />
            <div className="p-6 space-y-6">
                <div className="flex flex-col sm:flex-row justify-between gap-4 sm:items-center">
                    <h1 className="text-xl font-semibold text-gray-800 dark:text-white">Daftar Guru</h1>
                    <div className="flex flex-wrap gap-2">
                        <input
                            type="text"
                            placeholder="Cari nama/NIP/mapel..."
                            className="w-full sm:w-auto rounded-md border border-gray-300 bg-white px-4 py-2 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
                            value={search}
                            onChange={(e) => setSearch(e.target.value)}
                        />
                        <button
                            onClick={() => setIsModalOpen(true)}
                            className="rounded-md bg-blue-600 px-4 py-2 text-sm text-white hover:bg-blue-700 shadow-sm"
                        >
                            + Tambah Guru
                        </button>
                    </div>
                </div>

                <div className="rounded-xl border border-gray-200 bg-white p-4 shadow-md dark:border-gray-700 dark:bg-gray-900">
                    <DataTable
                        columns={columns}
                        data={filteredData}
                        pagination
                        responsive
                        highlightOnHover
                        striped
                        customStyles={customStyles}
                        noDataComponent="Data tidak ditemukan."
                    />
                </div>
            </div>

            {/* Modal */}
            {isModalOpen && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 px-4">
                    <div className="w-full max-w-md rounded-xl bg-white p-6 shadow-lg dark:bg-gray-800">
                        <h2 className="mb-4 text-lg font-semibold text-gray-800 dark:text-white">Tambah Guru</h2>
                        <div className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">Nama</label>
                                <input
                                    type="text"
                                    value={form.nama}
                                    onChange={(e) => setForm({ ...form, nama: e.target.value })}
                                    className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm dark:bg-gray-700 dark:text-white"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">NIP</label>
                                <input
                                    type="text"
                                    value={form.nip}
                                    onChange={(e) => setForm({ ...form, nip: e.target.value })}
                                    className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm dark:bg-gray-700 dark:text-white"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">Mata Pelajaran</label>
                                <input
                                    type="text"
                                    value={form.mapel}
                                    onChange={(e) => setForm({ ...form, mapel: e.target.value })}
                                    className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm dark:bg-gray-700 dark:text-white"
                                />
                            </div>
                            <div className="flex justify-end gap-2 pt-4">
                                <button
                                    onClick={() => setIsModalOpen(false)}
                                    className="rounded-md border border-gray-300 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:text-white dark:hover:bg-gray-700"
                                >
                                    Batal
                                </button>
                                <button
                                    onClick={handleAddGuru}
                                    className="rounded-md bg-blue-600 px-4 py-2 text-sm text-white hover:bg-blue-700"
                                >
                                    Simpan
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </AppLayout>
    );
}
