import { useState } from 'react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, router, usePage } from '@inertiajs/react';
import DataTable, { TableColumn } from 'react-data-table-component';
import { Search, Plus, Edit, Trash2, Award, Calendar, User, Trophy, Upload } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Prestasi',
        href: '/prestasi',
    },
];

// Type definition for student achievement data
type PrestasiSiswa = {
    id: number;
    nama_siswa: string;
    kelas?: string;
    jenis_prestasi: string;
    tingkat: 'Sekolah' | 'Kecamatan' | 'Kabupaten' | 'Provinsi' | 'Nasional' | 'Internasional';
    tahun: number;
    foto?: string;
    deskripsi?: string;
    tanggal_prestasi?: string;
    penyelenggara?: string;
};

// Sample data - replace with actual data from props or API
const sampleData: PrestasiSiswa[] = [
    {
        id: 1,
        nama_siswa: 'Ahmad Rizki',
        kelas: 'XII IPA 1',
        jenis_prestasi: 'Olimpiade Matematika',
        tingkat: 'Nasional',
        tahun: 2024,
        foto: 'prestasi1.jpg',
        deskripsi: 'Juara 1 Olimpiade Matematika Nasional',
        tanggal_prestasi: '2024-03-15',
        penyelenggara: 'Kemendikbud'
    },
    {
        id: 2,
        nama_siswa: 'Siti Nurhaliza',
        kelas: 'XI IPS 2',
        jenis_prestasi: 'Lomba Karya Tulis Ilmiah',
        tingkat: 'Provinsi',
        tahun: 2024,
        foto: 'prestasi2.jpg',
        deskripsi: 'Juara 2 Lomba Karya Tulis Ilmiah Tingkat Provinsi',
        tanggal_prestasi: '2024-02-20',
        penyelenggara: 'Dinas Pendidikan Provinsi'
    },
    {
        id: 3,
        nama_siswa: 'Budi Santoso',
        kelas: 'X MIPA 3',
        jenis_prestasi: 'Lomba Robotika',
        tingkat: 'Kabupaten',
        tahun: 2023,
        foto: 'prestasi3.jpg',
        deskripsi: 'Juara 3 Lomba Robotika Tingkat Kabupaten',
        tanggal_prestasi: '2023-11-10',
        penyelenggara: 'Dinas Pendidikan Kabupaten'
    },
    {
        id: 4,
        nama_siswa: 'Maya Sari',
        kelas: 'XII IPA 2',
        jenis_prestasi: 'Olimpiade Fisika',
        tingkat: 'Internasional',
        tahun: 2024,
        foto: 'prestasi4.jpg',
        deskripsi: 'Medali Perunggu Olimpiade Fisika Internasional',
        tanggal_prestasi: '2024-07-12',
        penyelenggara: 'International Physics Olympiad'
    }
];

interface PrestasiPageProps {
    prestasi: PrestasiSiswa[];
    [key: string]: any;
}

export default function Prestasi() {
    const { prestasi = [] } = usePage<PrestasiPageProps>().props;
    const [data, setData] = useState<PrestasiSiswa[]>(prestasi.length > 0 ? prestasi : sampleData);
    const [search, setSearch] = useState('');
    const [filterTingkat, setFilterTingkat] = useState('');
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [formData, setFormData] = useState({
        nama_siswa: '',
        jenis_prestasi: '',
        tingkat: '',
        tahun: new Date().getFullYear(),
        peringkat: '',
        foto: null as File | null
    });

    // Filter data based on search and tingkat filter
    const filteredData = data.filter(item => {
        const matchesSearch =
            item.nama_siswa.toLowerCase().includes(search.toLowerCase()) ||
            item.jenis_prestasi.toLowerCase().includes(search.toLowerCase()) ||
            item.kelas?.toLowerCase().includes(search.toLowerCase()) ||
            item.penyelenggara?.toLowerCase().includes(search.toLowerCase());

        const matchesTingkat = filterTingkat === '' || item.tingkat === filterTingkat;

        return matchesSearch && matchesTingkat;
    });

    // Get badge color based on achievement level
    const getTingkatBadgeColor = (tingkat: string) => {
        switch (tingkat) {
            case 'Internasional':
                return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
            case 'Nasional':
                return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
            case 'Provinsi':
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
            case 'Kabupaten':
                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
            case 'Kecamatan':
                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
            case 'Sekolah':
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
        }
    };

    // Form handling functions
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: name === 'tahun' ? parseInt(value) : value
        }));
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0] || null;
        setFormData(prev => ({
            ...prev,
            foto: file
        }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        const submitData = new FormData();
        submitData.append('nama_siswa', formData.nama_siswa);
        submitData.append('jenis_prestasi', formData.jenis_prestasi);
        submitData.append('tingkat', formData.tingkat);
        submitData.append('tahun', formData.tahun.toString());
        submitData.append('peringkat', formData.peringkat);

        if (formData.foto) {
            submitData.append('foto', formData.foto);
        }

        router.post('/prestasi', submitData, {
            forceFormData: true,
            onSuccess: () => {
                setIsModalOpen(false);
                setFormData({
                    nama_siswa: '',
                    jenis_prestasi: '',
                    tingkat: '',
                    tahun: new Date().getFullYear(),
                    peringkat: '',
                    foto: null
                });
            },
            onError: (errors) => {
                console.error('Form errors:', errors);
            }
        });
    };

    const handleDelete = (id: number) => {
        if (confirm('Yakin ingin menghapus prestasi ini?')) {
            router.delete(`/prestasi/${id}`);
        }
    };

    // Table columns configuration
    const columns: TableColumn<PrestasiSiswa>[] = [
        {
            name: 'No',
            cell: (_row, index) => (
                <div className="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold dark:bg-blue-900 dark:text-blue-200">
                    {index + 1}
                </div>
            ),
            width: '70px',
            center: true,
        },
        {
            name: 'Siswa',
            cell: (row) => (
                <div className="flex items-center space-x-3 py-2">
                    <div className="flex-shrink-0">
                        <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                            <User className="w-5 h-5" />
                        </div>
                    </div>
                    <div className="flex-1 min-w-0">
                        <p className="text-sm font-semibold text-gray-900 dark:text-white truncate">
                            {row.nama_siswa}
                        </p>
                        {row.kelas && (
                            <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                                Kelas {row.kelas}
                            </p>
                        )}
                    </div>
                </div>
            ),
            sortable: true,
            selector: (row) => row.nama_siswa,
            minWidth: '200px',
        },
        {
            name: 'Prestasi',
            cell: (row) => (
                <div className="py-2">
                    <div className="flex items-center space-x-2 mb-1">
                        <Trophy className="w-4 h-4 text-yellow-500" />
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {row.jenis_prestasi}
                        </p>
                    </div>
                    {row.deskripsi && (
                        <p className="text-xs text-gray-500 dark:text-gray-400 line-clamp-2">
                            {row.deskripsi}
                        </p>
                    )}
                    {row.penyelenggara && (
                        <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                            {row.penyelenggara}
                        </p>
                    )}
                </div>
            ),
            sortable: true,
            selector: (row) => row.jenis_prestasi,
            minWidth: '250px',
            wrap: true,
        },
        {
            name: 'Tingkat',
            cell: (row) => (
                <div className="flex items-center justify-center">
                    <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold ${getTingkatBadgeColor(row.tingkat)}`}>
                        <Award className="w-3 h-3 mr-1" />
                        {row.tingkat}
                    </span>
                </div>
            ),
            sortable: true,
            selector: (row) => row.tingkat,
            center: true,
            width: '140px',
        },
        {
            name: 'Tahun',
            cell: (row) => (
                <div className="flex items-center justify-center space-x-1">
                    <Calendar className="w-4 h-4 text-gray-500" />
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {row.tahun}
                    </span>
                </div>
            ),
            sortable: true,
            selector: (row) => row.tahun,
            center: true,
            width: '100px',
        },
        {
            name: 'Foto',
            cell: (row) => (
                <div className="flex justify-center py-2">
                    {row.foto ? (
                        <div className="relative group">
                            <img
                                src={`/storage/prestasi/${row.foto}`}
                                alt={`Prestasi ${row.nama_siswa}`}
                                className="w-16 h-16 object-cover rounded-lg shadow-md border-2 border-gray-200 dark:border-gray-700 transition-transform group-hover:scale-105"
                                onError={(e) => {
                                    const target = e.target as HTMLImageElement;
                                    target.src = '/images/no-image.png';
                                }}
                            />
                            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-lg transition-all duration-200 flex items-center justify-center">
                                <span className="text-white text-xs opacity-0 group-hover:opacity-100 transition-opacity">
                                    Lihat
                                </span>
                            </div>
                        </div>
                    ) : (
                        <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-gray-600">
                            <span className="text-xs text-gray-400">No Image</span>
                        </div>
                    )}
                </div>
            ),
            center: true,
            width: '100px',
        },
        {
            name: 'Aksi',
            cell: (row) => (
                <div className="flex items-center justify-center space-x-1">
                    <Button
                        variant="outline"
                        size="sm"
                        className="h-8 w-8 p-0 hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900"
                        title="Edit"
                    >
                        <Edit className="h-3 w-3 text-blue-600" />
                    </Button>
                    <Button
                        variant="outline"
                        size="sm"
                        className="h-8 w-8 p-0 hover:bg-red-50 hover:border-red-300 dark:hover:bg-red-900"
                        title="Hapus"
                        onClick={() => handleDelete(row.id)}
                    >
                        <Trash2 className="h-3 w-3 text-red-600" />
                    </Button>
                </div>
            ),
            center: true,
            width: '100px',
        },
    ];

    // Custom styles for the data table
    const customStyles = {
        headCells: {
            style: {
                fontWeight: '600',
                fontSize: '14px',
                backgroundColor: '#f8fafc',
                color: '#374151',
                borderBottom: '2px solid #e5e7eb',
                paddingTop: '12px',
                paddingBottom: '12px',
            },
        },
        cells: {
            style: {
                fontSize: '14px',
                paddingTop: '8px',
                paddingBottom: '8px',
                borderBottom: '1px solid #f3f4f6',
            },
        },
        rows: {
            style: {
                minHeight: '60px',
                '&:hover': {
                    backgroundColor: '#f9fafb',
                    cursor: 'pointer',
                },
            },
            stripedStyle: {
                backgroundColor: '#fafafa',
            },
        },
        pagination: {
            style: {
                borderTop: '2px solid #e5e7eb',
                paddingTop: '16px',
                paddingBottom: '16px',
            },
        },
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Prestasi Siswa" />

            <div className="p-6 space-y-6">
                {/* Header Section */}
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                            Prestasi Siswa
                        </h1>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            Kelola dan lihat prestasi yang diraih oleh siswa-siswi sekolah
                        </p>
                    </div>
                    <Button
                        className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700"
                        onClick={() => setIsModalOpen(true)}
                    >
                        <Plus className="w-4 h-4" />
                        <span>Tambah Prestasi</span>
                    </Button>
                </div>

                {/* Statistics Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 shadow-sm">
                        <div className="flex items-center">
                            <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                                <Trophy className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                            </div>
                            <div className="ml-3">
                                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Prestasi</p>
                                <p className="text-2xl font-bold text-gray-900 dark:text-white">{data.length}</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 shadow-sm">
                        <div className="flex items-center">
                            <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                                <Award className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                            </div>
                            <div className="ml-3">
                                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Internasional</p>
                                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                                    {data.filter(item => item.tingkat === 'Internasional').length}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 shadow-sm">
                        <div className="flex items-center">
                            <div className="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
                                <Award className="w-6 h-6 text-red-600 dark:text-red-400" />
                            </div>
                            <div className="ml-3">
                                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Nasional</p>
                                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                                    {data.filter(item => item.tingkat === 'Nasional').length}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 shadow-sm">
                        <div className="flex items-center">
                            <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                                <Calendar className="w-6 h-6 text-green-600 dark:text-green-400" />
                            </div>
                            <div className="ml-3">
                                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Tahun Ini</p>
                                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                                    {data.filter(item => item.tahun === new Date().getFullYear()).length}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Filters Section */}
                <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 shadow-sm">
                    <div className="flex flex-col sm:flex-row gap-4">
                        <div className="flex-1">
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                                <Input
                                    type="text"
                                    placeholder="Cari nama siswa, jenis prestasi, atau penyelenggara..."
                                    value={search}
                                    onChange={(e) => setSearch(e.target.value)}
                                    className="pl-10 pr-4 py-2 w-full"
                                />
                            </div>
                        </div>
                        <div className="sm:w-48">
                            <select
                                value={filterTingkat}
                                onChange={(e) => setFilterTingkat(e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            >
                                <option value="">Semua Tingkat</option>
                                <option value="Internasional">Internasional</option>
                                <option value="Nasional">Nasional</option>
                                <option value="Provinsi">Provinsi</option>
                                <option value="Kabupaten">Kabupaten</option>
                                <option value="Kecamatan">Kecamatan</option>
                                <option value="Sekolah">Sekolah</option>
                            </select>
                        </div>
                    </div>

                    {/* Filter Results Info */}
                    <div className="mt-3 flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                        <span>
                            Menampilkan {filteredData.length} dari {data.length} prestasi
                        </span>
                        {(search || filterTingkat) && (
                            <button
                                onClick={() => {
                                    setSearch('');
                                    setFilterTingkat('');
                                }}
                                className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                            >
                                Reset Filter
                            </button>
                        )}
                    </div>
                </div>

                {/* Data Table */}
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden">
                    <DataTable
                        columns={columns}
                        data={filteredData}
                        pagination
                        paginationPerPage={10}
                        paginationRowsPerPageOptions={[5, 10, 15, 20]}
                        responsive
                        highlightOnHover
                        striped
                        customStyles={customStyles}
                        noDataComponent={
                            <div className="flex flex-col items-center justify-center py-12">
                                <Trophy className="w-12 h-12 text-gray-400 mb-4" />
                                <p className="text-gray-500 dark:text-gray-400 text-lg font-medium">
                                    {search || filterTingkat ? 'Tidak ada prestasi yang sesuai dengan filter' : 'Belum ada data prestasi'}
                                </p>
                                <p className="text-gray-400 dark:text-gray-500 text-sm mt-1">
                                    {search || filterTingkat ? 'Coba ubah kata kunci pencarian atau filter' : 'Tambahkan prestasi siswa untuk mulai mengelola data'}
                                </p>
                            </div>
                        }
                        paginationComponentOptions={{
                            rowsPerPageText: 'Baris per halaman:',
                            rangeSeparatorText: 'dari',
                            noRowsPerPage: false,
                            selectAllRowsItem: false,
                        }}
                    />
                </div>

                {/* Modal Form Tambah Prestasi */}
                <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
                    <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
                        <DialogHeader>
                            <DialogTitle className="flex items-center space-x-2">
                                <Trophy className="w-5 h-5 text-blue-600" />
                                <span>Tambah Prestasi Siswa</span>
                            </DialogTitle>
                            <DialogDescription>
                                Tambahkan prestasi baru yang diraih oleh siswa. Pastikan semua data terisi dengan benar.
                            </DialogDescription>
                        </DialogHeader>

                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {/* Nama Siswa */}
                                <div className="space-y-2">
                                    <label htmlFor="nama_siswa" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Nama Siswa *
                                    </label>
                                    <Input
                                        id="nama_siswa"
                                        name="nama_siswa"
                                        type="text"
                                        value={formData.nama_siswa}
                                        onChange={handleInputChange}
                                        placeholder="Masukkan nama siswa"
                                        required
                                        className="w-full"
                                    />
                                </div>

                                {/* Jenis Prestasi */}
                                <div className="space-y-2">
                                    <label htmlFor="jenis_prestasi" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Jenis Prestasi *
                                    </label>
                                    <Input
                                        id="jenis_prestasi"
                                        name="jenis_prestasi"
                                        type="text"
                                        value={formData.jenis_prestasi}
                                        onChange={handleInputChange}
                                        placeholder="Contoh: Olimpiade Matematika"
                                        required
                                        className="w-full"
                                    />
                                </div>

                                {/* Tingkat Prestasi */}
                                <div className="space-y-2">
                                    <label htmlFor="tingkat" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Tingkat Prestasi *
                                    </label>
                                    <select
                                        id="tingkat"
                                        name="tingkat"
                                        value={formData.tingkat}
                                        onChange={handleInputChange}
                                        required
                                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    >
                                        <option value="">Pilih Tingkat</option>
                                        <option value="Sekolah">Sekolah</option>
                                        <option value="Kecamatan">Kecamatan</option>
                                        <option value="Kabupaten">Kabupaten</option>
                                        <option value="Provinsi">Provinsi</option>
                                        <option value="Nasional">Nasional</option>
                                        <option value="Internasional">Internasional</option>
                                    </select>
                                </div>

                                {/* Tahun */}
                                <div className="space-y-2">
                                    <label htmlFor="tahun" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Tahun *
                                    </label>
                                    <Input
                                        id="tahun"
                                        name="tahun"
                                        type="number"
                                        value={formData.tahun}
                                        onChange={handleInputChange}
                                        min="2000"
                                        max={new Date().getFullYear() + 1}
                                        required
                                        className="w-full"
                                    />
                                </div>
                            </div>

                            {/* Peringkat */}
                            <div className="space-y-2">
                                <label htmlFor="peringkat" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Peringkat/Juara
                                </label>
                                <Input
                                    id="peringkat"
                                    name="peringkat"
                                    type="text"
                                    value={formData.peringkat}
                                    onChange={handleInputChange}
                                    placeholder="Contoh: Juara 1, Medali Emas, dll"
                                    className="w-full"
                                />
                            </div>

                            {/* Upload Foto */}
                            <div className="space-y-2">
                                <label htmlFor="foto" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Foto Prestasi
                                </label>
                                <div className="flex items-center justify-center w-full">
                                    <label htmlFor="foto" className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-gray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500">
                                        <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                            <Upload className="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" />
                                            <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">
                                                <span className="font-semibold">Klik untuk upload</span> atau drag and drop
                                            </p>
                                            <p className="text-xs text-gray-500 dark:text-gray-400">PNG, JPG, GIF (MAX. 2MB)</p>
                                            {formData.foto && (
                                                <p className="text-xs text-blue-600 dark:text-blue-400 mt-2">
                                                    File terpilih: {formData.foto.name}
                                                </p>
                                            )}
                                        </div>
                                        <input
                                            id="foto"
                                            name="foto"
                                            type="file"
                                            className="hidden"
                                            accept="image/*"
                                            onChange={handleFileChange}
                                        />
                                    </label>
                                </div>
                            </div>

                            {/* Form Actions */}
                            <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => setIsModalOpen(false)}
                                >
                                    Batal
                                </Button>
                                <Button
                                    type="submit"
                                    className="bg-blue-600 hover:bg-blue-700"
                                >
                                    <Trophy className="w-4 h-4 mr-2" />
                                    Simpan Prestasi
                                </Button>
                            </div>
                        </form>
                    </DialogContent>
                </Dialog>
            </div>
        </AppLayout>
    );
}
