import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Trophy, Star, Medal, Award, Crown, User, Calendar } from 'lucide-react';

interface PrestasiItem {
    id: number;
    nama_siswa: string;
    jenis_prestasi: string;
    tahun: number;
    tingkat: string;
    peringkat: string;
    foto?: string | null;
}

interface PrestasiSectionProps {
    initialPrestasi?: PrestasiItem[];
}

export default function PrestasiSection({ initialPrestasi = [] }: PrestasiSectionProps) {
    const [prestasi, setPrestasi] = useState<PrestasiItem[]>(initialPrestasi);
    const [loading, setLoading] = useState(false);

    // Polling untuk update data prestasi secara real-time
    useEffect(() => {
        const fetchPrestasi = async () => {
            try {
                setLoading(true);
                const response = await fetch('/api/prestasi');
                const data = await response.json();
                if (data.success) {
                    setPrestasi(data.data);
                }
            } catch (error) {
                console.error('Error fetching prestasi:', error);
            } finally {
                setLoading(false);
            }
        };

        // Fetch initial data if not provided
        if (initialPrestasi.length === 0) {
            fetchPrestasi();
        }

        // Set up polling every 30 seconds
        const interval = setInterval(fetchPrestasi, 30000);
        return () => clearInterval(interval);
    }, [initialPrestasi.length]);

    // Function to get tingkat color
    const getTingkatColor = (tingkat: string) => {
        switch (tingkat.toLowerCase()) {
            case 'internasional':
                return 'bg-gradient-to-r from-purple-500 to-pink-500 text-white';
            case 'nasional':
                return 'bg-gradient-to-r from-red-500 to-orange-500 text-white';
            case 'provinsi':
                return 'bg-gradient-to-r from-blue-500 to-indigo-500 text-white';
            case 'kabupaten':
                return 'bg-gradient-to-r from-green-500 to-teal-500 text-white';
            case 'kecamatan':
                return 'bg-gradient-to-r from-yellow-500 to-orange-400 text-white';
            default:
                return 'bg-gradient-to-r from-gray-500 to-gray-600 text-white';
        }
    };

    // Function to get achievement icon
    const getAchievementIcon = (tingkat: string) => {
        switch (tingkat.toLowerCase()) {
            case 'internasional':
                return Crown;
            case 'nasional':
                return Medal;
            case 'provinsi':
                return Award;
            default:
                return Trophy;
        }
    };

    return (
        <section id="prestasi" className="py-20 bg-gradient-to-br from-gray-50 via-white to-green-50/30 dark:from-gray-800 dark:via-gray-900 dark:to-green-900/10 relative overflow-hidden">
            {/* Background Decorations */}
            <div className="absolute inset-0 overflow-hidden">
                <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-yellow-200/20 to-orange-200/20 rounded-full blur-3xl"></div>
                <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-green-200/20 to-blue-200/20 rounded-full blur-3xl"></div>
            </div>

            <div className="mx-auto max-w-7xl px-4 relative z-10">
                {/* Section Header */}
                <motion.div
                    className="text-center mb-16"
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8 }}
                    viewport={{ once: true }}
                >
                    <motion.h3
                        className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-yellow-600 via-orange-600 to-red-600 bg-clip-text text-transparent"
                        whileHover={{ scale: 1.05 }}
                        transition={{ duration: 0.3 }}
                    >
                        Prestasi Membanggakan
                    </motion.h3>
                    <motion.div
                        className="w-24 h-1 bg-gradient-to-r from-yellow-500 to-orange-500 mx-auto rounded-full mb-6"
                        initial={{ width: 0 }}
                        whileInView={{ width: 96 }}
                        transition={{ duration: 0.8, delay: 0.2 }}
                        viewport={{ once: true }}
                    />
                    <p className="text-lg text-gray-700 dark:text-gray-200 max-w-2xl mx-auto leading-relaxed">
                        Kebanggaan kami atas pencapaian luar biasa siswa-siswi berprestasi yang mengharumkan nama sekolah
                    </p>
                </motion.div>

                {/* Loading State */}
                {loading && prestasi.length === 0 && (
                    <div className="flex justify-center items-center py-12">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-600"></div>
                    </div>
                )}

                {/* Prestasi Grid */}
                {prestasi.length > 0 ? (
                    <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                        {prestasi.map((item, index) => {
                            const IconComponent = getAchievementIcon(item.tingkat);
                            return (
                                <motion.div
                                    key={item.id}
                                    className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 group border border-gray-100 dark:border-gray-700 hover:border-yellow-200 dark:hover:border-yellow-700"
                                    initial={{ opacity: 0, y: 30, scale: 0.9 }}
                                    whileInView={{ opacity: 1, y: 0, scale: 1 }}
                                    transition={{ duration: 0.6, delay: index * 0.1 }}
                                    viewport={{ once: true }}
                                    whileHover={{ y: -8, scale: 1.02 }}
                                >
                                    {/* Student Photo */}
                                    {item.foto && (
                                        <div className="mb-4 flex justify-center">
                                            <div className="relative">
                                                <img
                                                    src={item.foto}
                                                    alt={item.nama_siswa}
                                                    className="w-16 h-16 rounded-full object-cover border-4 border-yellow-400 shadow-lg"
                                                    onError={(e) => {
                                                        const target = e.target as HTMLImageElement;
                                                        target.style.display = 'none';
                                                    }}
                                                />
                                                <div className="absolute -bottom-1 -right-1 bg-yellow-500 rounded-full p-1">
                                                    <IconComponent className="w-3 h-3 text-white" />
                                                </div>
                                            </div>
                                        </div>
                                    )}

                                    <div className="text-center">
                                        {/* Achievement Level Badge */}
                                        <div className="flex justify-center mb-3">
                                            <span className={`px-3 py-1 rounded-full text-xs font-bold ${getTingkatColor(item.tingkat)} shadow-lg`}>
                                                {item.tingkat}
                                            </span>
                                        </div>

                                        {/* Student Name */}
                                        <div className="flex items-center justify-center gap-2 mb-2">
                                            <User className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                                            <h4 className="font-bold text-lg text-gray-800 dark:text-white">
                                                {item.nama_siswa}
                                            </h4>
                                        </div>

                                        {/* Achievement Type */}
                                        <p className="text-gray-700 dark:text-gray-200 text-sm mb-3 leading-relaxed font-medium">
                                            {item.jenis_prestasi}
                                        </p>

                                        {/* Achievement Details */}
                                        <div className="space-y-2">
                                            <div className="flex items-center justify-center gap-2">
                                                <Star className="w-4 h-4 text-yellow-500 fill-current" />
                                                <span className="font-semibold text-yellow-600 dark:text-yellow-400">
                                                    {item.peringkat}
                                                </span>
                                            </div>
                                            <div className="flex items-center justify-center gap-2">
                                                <Calendar className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                                                <span className="text-gray-600 dark:text-gray-300 text-sm">
                                                    {item.tahun}
                                                </span>
                                            </div>
                                        </div>

                                        {/* Achievement Icon */}
                                        <div className="mt-4 flex justify-center">
                                            <div className="bg-gradient-to-r from-yellow-400 to-orange-500 p-3 rounded-full shadow-lg group-hover:scale-110 transition-transform duration-300">
                                                <IconComponent className="w-6 h-6 text-white" />
                                            </div>
                                        </div>
                                    </div>
                                </motion.div>
                            );
                        })}
                    </div>
                ) : !loading && (
                    <motion.div
                        className="text-center py-12"
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        transition={{ duration: 0.6 }}
                        viewport={{ once: true }}
                    >
                        <Trophy className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500 dark:text-gray-400 text-lg">
                            Belum ada data prestasi yang tersedia
                        </p>
                    </motion.div>
                )}

                {/* Call to Action */}
                <motion.div
                    className="text-center mt-16"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    viewport={{ once: true }}
                >
                    <motion.button
                        className="bg-gradient-to-r from-yellow-500 via-orange-500 to-red-500 hover:from-yellow-600 hover:via-orange-600 hover:to-red-600 text-white px-8 py-4 rounded-full font-bold shadow-lg hover:shadow-xl transform transition-all duration-300 text-lg"
                        whileHover={{ scale: 1.05, y: -2 }}
                        whileTap={{ scale: 0.95 }}
                    >
                        <div className="flex items-center gap-2">
                            <Trophy className="w-5 h-5" />
                            Lihat Semua Prestasi
                        </div>
                    </motion.button>
                </motion.div>
            </div>
        </section>
    );
}