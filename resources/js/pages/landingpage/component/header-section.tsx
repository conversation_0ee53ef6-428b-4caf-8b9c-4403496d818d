import { useState, useEffect } from 'react';
import { Link } from '@inertiajs/react';
import { motion, AnimatePresence } from 'framer-motion';
import {
    Menu,
    X,
    ChevronRight
} from 'lucide-react';

interface HeaderSectionProps {
    activeSection: string;
    scrollToSection: (sectionId: string) => void;
}

export default function HeaderSection({ activeSection, scrollToSection }: HeaderSectionProps) {
    const [menuOpen, setMenuOpen] = useState(false);
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const [digitalDropdownOpen, setDigitalDropdownOpen] = useState(false);

    // Close dropdown when clicking outside or when mobile menu closes
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            const target = event.target as Element;
            if ((dropdownOpen || digitalDropdownOpen) && !target.closest('.dropdown-container')) {
                setDropdownOpen(false);
                setDigitalDropdownOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, [dropdownOpen, digitalDropdownOpen]);

    // Close dropdown when mobile menu closes
    useEffect(() => {
        if (!menuOpen) {
            setDropdownOpen(false);
            setDigitalDropdownOpen(false);
        }
    }, [menuOpen]);

    const handleScrollToSection = (sectionId: string) => {
        scrollToSection(sectionId);
        setMenuOpen(false);
    };

    return (
        <header className="sticky top-0 z-50 bg-gradient-to-r from-green-700 to-green-600 backdrop-blur-sm shadow-lg transition-all duration-300">
            <div className="mx-auto flex max-w-7xl items-center justify-between px-6 py-4">
                <motion.div
                    className="flex items-center gap-3"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5 }}
                >
                    <img
                        src="/assets/logo_mts.png"
                        alt="Logo Sekolah"
                        className="h-10 w-10 object-cover rounded-full"
                    />
                    <div>
                        <h1 className="text-xl font-bold text-white">MTs Negeri 4 Gunungkidul</h1>
                        <p className="text-xs text-white/80">Excellence in Education</p>
                    </div>
                </motion.div>

                {/* Tombol Mobile */}
                <div className="lg:hidden">
                    <button
                        onClick={() => setMenuOpen(!menuOpen)}
                        className="p-2 rounded-lg hover:bg-green-800/30 text-white transition-colors focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-offset-2 focus:ring-offset-green-700"
                    >
                        {menuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
                    </button>
                </div>

                {/* Menu Desktop */}
                <nav className="hidden lg:flex items-center gap-8 text-sm font-medium text-white">
                    {['Beranda','Tentang', 'Berita', 'Prestasi', 'Kontak'].map((menu) => {
                        const isActive = activeSection === menu.toLowerCase();
                        return (
                            <button
                                key={menu}
                                onClick={() => handleScrollToSection(menu.toLowerCase())}
                                className={`hover:text-yellow-300 relative group transition-colors duration-200 ${
                                    isActive ? 'text-yellow-300' : ''
                                }`}
                            >
                                {menu}
                                <span className={`absolute -bottom-1 left-0 h-0.5 bg-yellow-300 transition-all duration-300 ${
                                    isActive ? 'w-full' : 'w-0 group-hover:w-full'
                                }`}></span>
                            </button>
                        );
                    })}
                    <a
                        href="https://ppdb.mtsn4gk.sch.id/"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="hover:text-yellow-300 relative group transition-colors duration-200"
                    >
                        PPDB
                        <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-yellow-300 transition-all duration-300 group-hover:w-full"></span>
                    </a>
                    <div className="relative dropdown-container">
                        <button
                            onClick={() => setDigitalDropdownOpen(!digitalDropdownOpen)}
                            className="hover:text-yellow-300 relative group transition-colors duration-200 flex items-center gap-1"
                        >
                            Digital
                            <ChevronRight className={`w-4 h-4 transition-transform duration-200 ${digitalDropdownOpen ? 'rotate-90' : ''}`} />
                            <span className={`absolute -bottom-1 left-0 h-0.5 bg-yellow-300 transition-all duration-300 ${
                                digitalDropdownOpen ? 'w-full' : 'w-0 group-hover:w-full'
                            }`}></span>
                        </button>

                        <AnimatePresence>
                            {digitalDropdownOpen && (
                                <motion.div
                                    className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-xl border border-gray-100 py-2 z-50 backdrop-blur-sm"
                                    initial={{ opacity: 0, y: -10, scale: 0.95 }}
                                    animate={{ opacity: 1, y: 0, scale: 1 }}
                                    exit={{ opacity: 0, y: -10, scale: 0.95 }}
                                    transition={{ duration: 0.2 }}
                                >
                                    <a
                                        href="#"
                                        onClick={(e) => {
                                            e.preventDefault();
                                            setDigitalDropdownOpen(false);
                                            alert('Modul Pembelajaran akan segera tersedia!');
                                        }}
                                        className="block px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors"
                                    >
                                        Modul Pembelajaran
                                    </a>
                                </motion.div>
                            )}
                        </AnimatePresence>
                    </div>
                    <div className="relative dropdown-container">
                        <button
                            onClick={() => setDropdownOpen(!dropdownOpen)}
                            className="hover:text-yellow-300 relative group transition-colors duration-200 flex items-center gap-1"
                        >
                            Lain-Lain
                            <ChevronRight className={`w-4 h-4 transition-transform duration-200 ${dropdownOpen ? 'rotate-90' : ''}`} />
                            <span className={`absolute -bottom-1 left-0 h-0.5 bg-yellow-300 transition-all duration-300 ${
                                dropdownOpen ? 'w-full' : 'w-0 group-hover:w-full'
                            }`}></span>
                        </button>

                        <AnimatePresence>
                            {dropdownOpen && (
                                <motion.div
                                    className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-xl border border-gray-100 py-2 z-50 backdrop-blur-sm"
                                    initial={{ opacity: 0, y: -10, scale: 0.95 }}
                                    animate={{ opacity: 1, y: 0, scale: 1 }}
                                    exit={{ opacity: 0, y: -10, scale: 0.95 }}
                                    transition={{ duration: 0.2 }}
                                >
                                    <Link
                                        href="/login"
                                        className="block px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors"
                                        onClick={() => setDropdownOpen(false)}
                                    >
                                        Login Admin
                                    </Link>
                                </motion.div>
                            )}
                        </AnimatePresence>
                    </div>
                </nav>
            </div>

            {/* Mobile menu */}
            <AnimatePresence>
                {menuOpen && (
                    <motion.div
                        className="lg:hidden border-t border-white/20 text-white"
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                    >
                        <div className="px-6 py-4 space-y-3">
                            {['Beranda','Tentang', 'Berita', 'Prestasi', 'Kontak'].map((menu) => {
                                const isActive = activeSection === menu.toLowerCase();
                                return (
                                    <button
                                        key={menu}
                                        onClick={() => handleScrollToSection(menu.toLowerCase())}
                                        className={`block py-2 hover:text-yellow-300 transition-colors w-full text-left ${
                                            isActive ? 'text-yellow-300 font-semibold' : ''
                                        }`}
                                    >
                                        {menu}
                                    </button>
                                );
                            })}
                            <a
                                href="https://ppdb.mtsn4gk.sch.id/"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="block py-2 hover:text-yellow-300 transition-colors"
                            >
                                PPDB
                            </a>
                            <div className="relative dropdown-container">
                                <button
                                    onClick={() => setDigitalDropdownOpen(!digitalDropdownOpen)}
                                    className="block py-2 hover:text-yellow-300 transition-colors w-full text-left flex items-center justify-between"
                                >
                                    Digital
                                    <ChevronRight className={`w-4 h-4 transition-transform duration-200 ${digitalDropdownOpen ? 'rotate-90' : ''}`} />
                                </button>
                                {digitalDropdownOpen && (
                                    <div className="ml-4 mt-2 space-y-2">
                                        <a
                                            href="#"
                                            onClick={(e) => {
                                                e.preventDefault();
                                                setMenuOpen(false);
                                                setDigitalDropdownOpen(false);
                                                alert('Modul Pembelajaran akan segera tersedia!');
                                            }}
                                            className="block py-2 text-white/80 hover:text-yellow-300 transition-colors text-sm"
                                        >
                                            Modul Pembelajaran
                                        </a>
                                    </div>
                                )}
                            </div>
                            <div className="relative dropdown-container">
                                <button
                                    onClick={() => setDropdownOpen(!dropdownOpen)}
                                    className="block py-2 hover:text-yellow-300 transition-colors w-full text-left flex items-center justify-between"
                                >
                                    Lain-lain
                                    <ChevronRight className={`w-4 h-4 transition-transform duration-200 ${dropdownOpen ? 'rotate-90' : ''}`} />
                                </button>
                                {dropdownOpen && (
                                    <div className="ml-4 mt-2 space-y-2">
                                        <Link
                                            href="/login"
                                            className="block py-2 text-white/80 hover:text-yellow-300 transition-colors text-sm"
                                            onClick={() => {
                                                setMenuOpen(false);
                                                setDropdownOpen(false);
                                            }}
                                        >
                                            Login Admin
                                        </Link>
                                    </div>
                                )}
                            </div>
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>
        </header>
    );
}