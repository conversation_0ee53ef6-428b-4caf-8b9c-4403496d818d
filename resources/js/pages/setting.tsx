import { useState, useEffect } from 'react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm, usePage } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ChevronDown, ChevronUp, Image, FileText, Save, Upload, X, CheckCircle, AlertCircle, Plus, Trash2 } from 'lucide-react';
import { cn } from '@/lib/utils';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Setting',
        href: '/setting',
    },
];

interface ImagePreview {
    id: string;
    file: File;
    preview: string;
}

interface PageProps {
    headerSettings?: {
        header_images?: string[] | null;
        header_description?: string;
    };
    flash?: {
        success?: string;
        error?: string;
    };
    [key: string]: any;
}

export default function Setting() {
    const { props } = usePage<PageProps>();
    const [isHeaderOpen, setIsHeaderOpen] = useState(false);
    const [imagePreviews, setImagePreviews] = useState<ImagePreview[]>([]);

    const { data, setData, post, processing, errors } = useForm({
        header_images: [] as File[],
        header_description: props.headerSettings?.header_description || '',
    });

    // Set initial image previews if exists
    useEffect(() => {
        if (props.headerSettings?.header_images && props.headerSettings.header_images.length > 0) {
            const initialPreviews: ImagePreview[] = props.headerSettings.header_images.map((imageUrl, index) => ({
                id: `existing-${index}`,
                file: null as any, // For existing images, we don't have the file
                preview: imageUrl
            }));
            setImagePreviews(initialPreviews);
        }
    }, [props.headerSettings]);

    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            const id = `new-${Date.now()}`;
            const newFiles = [...data.header_images, file];

            // Create preview
            const reader = new FileReader();
            reader.onload = (e) => {
                const preview: ImagePreview = {
                    id,
                    file,
                    preview: e.target?.result as string
                };

                setImagePreviews(prev => [...prev, preview]);
                setData('header_images', newFiles);
            };
            reader.readAsDataURL(file);
        }

        // Reset file input
        e.target.value = '';
    };

    const removeImage = async (imageId: string) => {
        const imageToRemove = imagePreviews.find(img => img.id === imageId);

        if (imageToRemove && imageToRemove.id.startsWith('existing')) {
            // For existing images, make API call to delete from server
            try {
                // Extract filename from URL (e.g., "/storage/header/filename.jpg" -> "filename.jpg")
                const imageFilename = imageToRemove.preview.split('/').pop();

                const response = await fetch('/setting/header/image', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                    },
                    body: JSON.stringify({
                        image_filename: imageFilename
                    })
                });

                if (!response.ok) {
                    throw new Error('Failed to delete image');
                }

                // Remove from preview state
                setImagePreviews(prev => prev.filter(img => img.id !== imageId));

            } catch (error) {
                console.error('Error deleting image:', error);
                // You might want to show an error message to the user
            }
        } else {
            // For new images, just remove from state
            setImagePreviews(prev => {
                const updatedPreviews = prev.filter(img => img.id !== imageId);

                // Update form data to only include files that still have previews
                const updatedFiles = updatedPreviews
                    .filter(img => img.file) // Only include new files, not existing ones
                    .map(img => img.file);

                setData('header_images', updatedFiles);
                return updatedPreviews;
            });
        }
    };

    const removeAllImages = async () => {
        try {
            const response = await fetch('/setting/header/images/clear', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                }
            });

            if (!response.ok) {
                throw new Error('Failed to clear images');
            }

            // Clear preview state
            setImagePreviews([]);
            setData('header_images', []);

            // Reset file input
            const fileInput = document.getElementById('header_images') as HTMLInputElement;
            if (fileInput) {
                fileInput.value = '';
            }

        } catch (error) {
            console.error('Error clearing images:', error);
            // You might want to show an error message to the user
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post('/setting/header', {
            onSuccess: () => {
                // Handle success
                console.log('Header settings updated successfully');
            },
            onError: (errors) => {
                console.error('Error updating header settings:', errors);
            }
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Setting" />

            <div className="space-y-6">
                {/* Flash Messages */}
                {props.flash?.success && (
                    <Alert className="border-green-300 bg-gradient-to-r from-green-50 to-green-100 text-green-800 dark:from-green-900/20 dark:to-green-800/20 dark:text-green-300 shadow-lg animate-in slide-in-from-top-2 duration-500">
                        <div className="flex items-center gap-3">
                            <div className="p-1 bg-green-200 dark:bg-green-800 rounded-full">
                                <CheckCircle className="h-5 w-5 text-green-700 dark:text-green-300" />
                            </div>
                            <div>
                                <h4 className="font-semibold text-green-900 dark:text-green-200">Berhasil!</h4>
                                <AlertDescription className="text-green-700 dark:text-green-300 font-medium">
                                    {props.flash.success}
                                </AlertDescription>
                            </div>
                        </div>
                    </Alert>
                )}

                {props.flash?.error && (
                    <Alert className="border-red-300 bg-gradient-to-r from-red-50 to-red-100 text-red-800 dark:from-red-900/20 dark:to-red-800/20 dark:text-red-300 shadow-lg animate-in slide-in-from-top-2 duration-500">
                        <div className="flex items-center gap-3">
                            <div className="p-1 bg-red-200 dark:bg-red-800 rounded-full">
                                <AlertCircle className="h-5 w-5 text-red-700 dark:text-red-300" />
                            </div>
                            <div>
                                <h4 className="font-semibold text-red-900 dark:text-red-200">Terjadi Kesalahan!</h4>
                                <AlertDescription className="text-red-700 dark:text-red-300 font-medium">
                                    {props.flash.error}
                                </AlertDescription>
                            </div>
                        </div>
                    </Alert>
                )}

                {/* Header Settings Card */}
                <Card className="w-full max-w-6xl mx-auto shadow-lg border-0 bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
                    <Collapsible open={isHeaderOpen} onOpenChange={setIsHeaderOpen}>
                        <CollapsibleTrigger asChild>
                            <CardHeader className="m-2 cursor-pointer rounded-t-xl border-b border-gray-100 dark:border-gray-800 hover:bg-gradient-to-r hover:from-primary/5 hover:to-primary/10 transition-all duration-300">
                                <div className="flex items-center justify-between group">
                                    <div className="flex items-center gap-4">
                                        <div className="relative">
                                            <div className="p-3 bg-gradient-to-br from-primary/10 to-primary/20 rounded-xl shadow-sm group-hover:shadow-md transition-shadow duration-300">
                                                <Image className="w-6 h-6 text-primary" />
                                            </div>
                                            <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-900 animate-pulse"></div>
                                        </div>
                                        <div className="space-y-1">
                                            <CardTitle className="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-200 bg-clip-text text-transparent">
                                                Pengaturan Header Website
                                            </CardTitle>
                                            <CardDescription className="text-sm text-muted-foreground flex items-center gap-2">
                                                <FileText className="w-4 h-4" />
                                                Kelola gambar dan deskripsi header untuk tampilan yang menarik
                                            </CardDescription>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <div className={cn(
                                            "px-3 py-1 rounded-full text-xs font-medium transition-all duration-300",
                                            isHeaderOpen
                                                ? "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
                                                : "bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400"
                                        )}>
                                            {isHeaderOpen ? "Terbuka" : "Tertutup"}
                                        </div>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            className="p-2 rounded-full hover:bg-primary/10 transition-all duration-300 group-hover:scale-105"
                                        >
                                            {isHeaderOpen ? (
                                                <ChevronUp className="w-5 h-5 text-primary transition-transform duration-300" />
                                            ) : (
                                                <ChevronDown className="w-5 h-5 text-muted-foreground group-hover:text-primary transition-all duration-300" />
                                            )}
                                        </Button>
                                    </div>
                                </div>
                            </CardHeader>
                        </CollapsibleTrigger>

                        <CollapsibleContent className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:slide-up-2 data-[state=open]:slide-down-2">
                            <CardContent className="pt-6 pb-8 px-8 bg-gradient-to-b from-gray-50/50 to-white dark:from-gray-800/50 dark:to-gray-900">
                                <form onSubmit={handleSubmit} className="space-y-8">
                                    {/* Image Upload Section */}
                                    <div className="space-y-6">
                                        <div className="flex items-center gap-3 pb-2 border-b border-gray-200 dark:border-gray-700">
                                            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                                                <Image className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                                            </div>
                                            <div>
                                                <label className="text-lg font-semibold text-gray-900 dark:text-white">
                                                    Gambar Header
                                                </label>
                                                <p className="text-sm text-muted-foreground">
                                                    Upload gambar untuk header website (Maks. 2MB)
                                                </p>
                                            </div>
                                        </div>

                                        <div className="space-y-6">
                                            {/* Images Preview */}
                                            {imagePreviews.length > 0 && (
                                                <div className="space-y-4">
                                                    <div className="flex items-center justify-between">
                                                        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                                                            Gambar Header ({imagePreviews.length})
                                                        </h4>
                                                        <Button
                                                            type="button"
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={removeAllImages}
                                                            className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20"
                                                        >
                                                            <Trash2 className="w-4 h-4 mr-1" />
                                                            Hapus Semua
                                                        </Button>
                                                    </div>

                                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                                        {imagePreviews.map((imagePreview) => (
                                                            <div key={imagePreview.id} className="relative group">
                                                                <div className="relative overflow-hidden rounded-xl border-2 border-gray-200 dark:border-gray-700 shadow-lg">
                                                                    <img
                                                                        src={imagePreview.preview}
                                                                        alt="Header preview"
                                                                        className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
                                                                    />
                                                                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300"></div>
                                                                    <Button
                                                                        type="button"
                                                                        variant="destructive"
                                                                        size="sm"
                                                                        className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-all duration-300 shadow-lg hover:scale-110"
                                                                        onClick={() => removeImage(imagePreview.id)}
                                                                    >
                                                                        <X className="w-3 h-3" />
                                                                    </Button>
                                                                    <div className="absolute bottom-2 left-2 bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-lg px-2 py-1 opacity-0 group-hover:opacity-100 transition-all duration-300">
                                                                        <p className="text-xs font-medium text-gray-700 dark:text-gray-300">
                                                                            {imagePreview.id.startsWith('existing') ? 'Gambar Lama' : 'Gambar Baru'}
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        ))}
                                                    </div>
                                                </div>
                                            )}

                                            {/* File Input */}
                                            <div className="relative group">
                                                <Input
                                                    id="header_images"
                                                    type="file"
                                                    accept="image/*"
                                                    onChange={handleImageChange}
                                                    className="hidden"
                                                />
                                                <label
                                                    htmlFor="header_images"
                                                    className={cn(
                                                        "flex flex-col items-center justify-center w-full h-40 border-2 border-dashed rounded-xl cursor-pointer transition-all duration-300 group-hover:scale-[1.02]",
                                                        imagePreviews.length > 0
                                                            ? "border-green-300 bg-green-50/50 hover:bg-green-100/50 dark:border-green-700 dark:bg-green-900/20 dark:hover:bg-green-900/30"
                                                            : "border-blue-300 bg-blue-50/50 hover:bg-blue-100/50 hover:border-blue-400 dark:border-blue-700 dark:bg-blue-900/20 dark:hover:bg-blue-900/30"
                                                    )}
                                                >
                                                    <div className="flex flex-col items-center justify-center py-6 px-4 text-center">
                                                        <div className={cn(
                                                            "p-3 rounded-full mb-4 transition-all duration-300 group-hover:scale-110",
                                                            imagePreviews.length > 0
                                                                ? "bg-green-100 dark:bg-green-900/40"
                                                                : "bg-blue-100 dark:bg-blue-900/40"
                                                        )}>
                                                            {imagePreviews.length > 0 ? (
                                                                <Plus className={cn(
                                                                    "w-8 h-8 transition-colors duration-300",
                                                                    "text-green-600 dark:text-green-400"
                                                                )} />
                                                            ) : (
                                                                <Upload className={cn(
                                                                    "w-8 h-8 transition-colors duration-300",
                                                                    "text-blue-600 dark:text-blue-400"
                                                                )} />
                                                            )}
                                                        </div>
                                                        <p className="mb-2 text-base font-semibold text-gray-700 dark:text-gray-300">
                                                            {imagePreviews.length > 0 ? "Tambah Gambar Lagi" : "Upload Gambar Header"}
                                                        </p>
                                                        <p className="text-sm text-muted-foreground mb-1">
                                                            <span className="font-medium">Klik untuk memilih gambar</span>
                                                        </p>
                                                        <p className="text-xs text-muted-foreground mb-2">
                                                            Pilih satu gambar per kali untuk menambah ke koleksi
                                                        </p>
                                                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                                            <span className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded-md">PNG</span>
                                                            <span className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded-md">JPG</span>
                                                            <span className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded-md">JPEG</span>
                                                            <span className="text-orange-600 dark:text-orange-400 font-medium">Max 2MB per file</span>
                                                        </div>
                                                    </div>
                                                </label>
                                            </div>

                                            {errors.header_images && (
                                                <div className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                                                    <AlertCircle className="w-4 h-4 text-red-600 dark:text-red-400" />
                                                    <p className="text-sm text-red-600 dark:text-red-400 font-medium">{errors.header_images}</p>
                                                </div>
                                            )}
                                        </div>
                                    </div>

                                    {/* Description Section */}
                                    <div className="space-y-6">
                                        <div className="flex items-center gap-3 pb-2 border-b border-gray-200 dark:border-gray-700">
                                            <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                                                <FileText className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                                            </div>
                                            <div>
                                                <label htmlFor="header_description" className="text-lg font-semibold text-gray-900 dark:text-white">
                                                    Deskripsi Header
                                                </label>
                                                <p className="text-sm text-muted-foreground">
                                                    Tulis deskripsi menarik untuk header website
                                                </p>
                                            </div>
                                        </div>

                                        <div className="space-y-4">
                                            <div className="relative">
                                                <textarea
                                                    id="header_description"
                                                    value={data.header_description}
                                                    onChange={(e) => setData('header_description', e.target.value)}
                                                    placeholder="Contoh: Selamat datang di website sekolah kami. Tempat dimana pendidikan berkualitas dan karakter terbentuk..."
                                                    className="flex min-h-[140px] w-full rounded-xl border-2 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 px-4 py-3 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-purple-500 focus-visible:border-purple-500 disabled:cursor-not-allowed disabled:opacity-50 resize-none transition-all duration-300"
                                                    rows={6}
                                                    maxLength={1000}
                                                />
                                                <div className="absolute bottom-3 right-3 text-xs text-muted-foreground bg-white dark:bg-gray-900 px-2 py-1 rounded-md border">
                                                    {data.header_description?.length || 0}/1000
                                                </div>
                                            </div>

                                            <div className="flex items-start gap-2 p-3 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg">
                                                <FileText className="w-4 h-4 text-purple-600 dark:text-purple-400 mt-0.5 flex-shrink-0" />
                                                <div className="text-sm text-purple-700 dark:text-purple-300">
                                                    <p className="font-medium mb-1">Tips menulis deskripsi yang baik:</p>
                                                    <ul className="text-xs space-y-1 text-purple-600 dark:text-purple-400">
                                                        <li>• Gunakan bahasa yang menarik dan mudah dipahami</li>
                                                        <li>• Jelaskan visi dan misi sekolah secara singkat</li>
                                                        <li>• Sertakan keunggulan atau prestasi sekolah</li>
                                                    </ul>
                                                </div>
                                            </div>

                                            {errors.header_description && (
                                                <div className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                                                    <AlertCircle className="w-4 h-4 text-red-600 dark:text-red-400" />
                                                    <p className="text-sm text-red-600 dark:text-red-400 font-medium">{errors.header_description}</p>
                                                </div>
                                            )}
                                        </div>
                                    </div>

                                    {/* Submit Button */}
                                    <div className="flex items-center justify-between pt-6 border-t-2 border-gray-100 dark:border-gray-800">
                                        <div className="text-sm text-muted-foreground">
                                            Pastikan semua data sudah benar sebelum menyimpan
                                        </div>
                                        <div className="flex gap-3">
                                            <Button
                                                type="button"
                                                variant="outline"
                                                onClick={() => setIsHeaderOpen(false)}
                                                className="flex items-center gap-2"
                                            >
                                                <X className="w-4 h-4" />
                                                Tutup
                                            </Button>
                                            <Button
                                                type="submit"
                                                disabled={processing}
                                                className="flex items-center gap-2 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 shadow-lg hover:shadow-xl transition-all duration-300 min-w-[140px]"
                                            >
                                                {processing ? (
                                                    <>
                                                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                                                        Menyimpan...
                                                    </>
                                                ) : (
                                                    <>
                                                        <Save className="w-4 h-4" />
                                                        Simpan Pengaturan
                                                    </>
                                                )}
                                            </Button>
                                        </div>
                                    </div>
                                </form>
                            </CardContent>
                        </CollapsibleContent>
                    </Collapsible>
                </Card>
            </div>
        </AppLayout>
    );
}
