<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('header', function (Blueprint $table) {
            // Change header_image to JSON to store multiple images
            $table->json('header_images')->nullable()->after('header_image');
        });
        
        // Migrate existing single image to array format
        DB::statement("
            UPDATE header 
            SET header_images = JSON_ARRAY(header_image) 
            WHERE header_image IS NOT NULL AND header_image != ''
        ");
        
        Schema::table('header', function (Blueprint $table) {
            // Drop the old single image column
            $table->dropColumn('header_image');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('header', function (Blueprint $table) {
            // Add back the single image column
            $table->string('header_image')->nullable()->after('id');
        });
        
        // Migrate first image from array back to single field
        DB::statement("
            UPDATE header 
            SET header_image = JSON_UNQUOTE(JSON_EXTRACT(header_images, '$[0]'))
            WHERE header_images IS NOT NULL AND JSON_LENGTH(header_images) > 0
        ");
        
        Schema::table('header', function (Blueprint $table) {
            // Drop the multiple images column
            $table->dropColumn('header_images');
        });
    }
};
