<?php

namespace App\Http\Controllers;
use Inertia\Inertia;
use App\Models\Prestasi;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class PrestasiController extends Controller
{
    public function index()
    {
        $prestasi = Prestasi::orderBy('created_at', 'desc')->get();

        return Inertia::render('prestasi', [
            'prestasi' => $prestasi
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'nama_siswa' => 'required|string|max:255',
            'jenis_prestasi' => 'required|string|max:255',
            'tingkat' => 'required|string|in:Sekolah,Kecamatan,Kabupaten,Provinsi,Nasional,Internasional',
            'tahun' => 'required|integer|min:2000|max:' . (date('Y') + 1),
            'peringkat' => 'nullable|string|max:255',
            'foto' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $data = $request->only(['nama_siswa', 'jenis_prestasi', 'tingkat', 'tahun', 'peringkat']);

        // Handle file upload
        if ($request->hasFile('foto')) {
            $file = $request->file('foto');
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('prestasi', $filename, 'public');
            $data['foto'] = $filename;
        }

        Prestasi::create($data);

        return redirect()->back()->with('success', 'Prestasi berhasil ditambahkan!');
    }

    public function destroy($id)
    {
        $prestasi = Prestasi::findOrFail($id);

        // Delete photo if exists
        if ($prestasi->foto) {
            Storage::disk('public')->delete('prestasi/' . $prestasi->foto);
        }

        $prestasi->delete();

        return redirect()->back()->with('success', 'Prestasi berhasil dihapus!');
    }

    // API endpoint untuk landing page
    public function getPrestasiForLanding()
    {
        $prestasi = Prestasi::orderBy('created_at', 'desc')
            ->limit(6)
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'nama_siswa' => $item->nama_siswa,
                    'jenis_prestasi' => $item->jenis_prestasi,
                    'tingkat' => $item->tingkat,
                    'tahun' => $item->tahun,
                    'peringkat' => $item->peringkat,
                    'foto' => $item->foto ? '/storage/prestasi/' . $item->foto : null,
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $prestasi
        ]);
    }
}
