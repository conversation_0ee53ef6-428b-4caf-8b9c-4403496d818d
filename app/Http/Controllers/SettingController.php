<?php

namespace App\Http\Controllers;
use Inertia\Inertia;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Models\Header;

class SettingController extends Controller
{
    public function index()
    {
        // Get current header settings from database
        $header = Header::first();

        $headerImages = [];
        if ($header && $header->header_images) {
            // Convert stored filenames to full URLs
            $headerImages = array_map(function($filename) {
                return Storage::exists('public/header/' . $filename)
                    ? '/storage/header/' . $filename
                    : null;
            }, $header->header_images);

            // Filter out null values
            $headerImages = array_filter($headerImages);
        }

        $headerSettings = [
            'header_images' => $headerImages,
            'header_description' => $header ? $header->header_deskripsi : '',
        ];

        return Inertia::render('setting', [
            'headerSettings' => $headerSettings
        ]);
    }

    public function updateHeader(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'header_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'header_description' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            $header = Header::first();
            $existingImages = $header && $header->header_images ? $header->header_images : [];

            // Handle image uploads (can be single or multiple files)
            if ($request->hasFile('header_images')) {
                $files = $request->file('header_images');
                $newImageFilenames = [];

                // Handle both single file and array of files
                if (!is_array($files)) {
                    $files = [$files];
                }

                foreach ($files as $index => $file) {
                    // Generate unique filename
                    $filename = 'header-image-' . time() . '-' . $index . '-' . uniqid() . '.' . $file->getClientOriginalExtension();

                    // Store the file
                    $file->storeAs('public/header', $filename);

                    // Add to new images array
                    $newImageFilenames[] = $filename;
                }

                // Add new images to existing images array
                $allImages = array_merge($existingImages, $newImageFilenames);

                // Update header images in database
                Header::updateOrCreate(
                    ['id' => 1],
                    ['header_images' => $allImages]
                );
            }

            // Handle description
            if ($request->has('header_description')) {
                Header::updateOrCreate(
                    ['id' => 1],
                    ['header_deskripsi' => $request->input('header_description')]
                );
            }

            return redirect()->back()->with('success', 'Pengaturan header berhasil diperbarui!');

        } catch (\Exception $e) {
            Log::error('Error updating header settings: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Terjadi kesalahan saat memperbarui pengaturan header.');
        }
    }

    public function deleteHeaderImage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'image_filename' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => 'Invalid request'], 400);
        }

        try {
            $header = Header::first();
            if (!$header || !$header->header_images) {
                return response()->json(['error' => 'No images found'], 404);
            }

            $imageFilename = $request->input('image_filename');
            $currentImages = $header->header_images;

            // Remove the image from the array
            $updatedImages = array_filter($currentImages, function($filename) use ($imageFilename) {
                return $filename !== $imageFilename;
            });

            // Re-index the array
            $updatedImages = array_values($updatedImages);

            // Delete the physical file
            if (Storage::exists('public/header/' . $imageFilename)) {
                Storage::delete('public/header/' . $imageFilename);
            }

            // Update database
            $header->update(['header_images' => $updatedImages]);

            return response()->json(['success' => 'Image deleted successfully']);

        } catch (\Exception $e) {
            Log::error('Error deleting header image: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to delete image'], 500);
        }
    }

    public function clearAllHeaderImages()
    {
        try {
            $header = Header::first();
            if (!$header || !$header->header_images) {
                return response()->json(['message' => 'No images to delete'], 200);
            }

            // Delete all physical files
            foreach ($header->header_images as $filename) {
                if (Storage::exists('public/header/' . $filename)) {
                    Storage::delete('public/header/' . $filename);
                }
            }

            // Clear images array in database
            $header->update(['header_images' => []]);

            return response()->json(['success' => 'All images deleted successfully']);

        } catch (\Exception $e) {
            Log::error('Error clearing all header images: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to delete images'], 500);
        }
    }
}
